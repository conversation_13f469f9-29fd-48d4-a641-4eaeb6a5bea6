# Project Guidelines

These guidelines are intended for AI agents and assistants, as well as human developers working on the project.

## Project Overview

This template serves as a starter project for websites built using the Brizy platform. The workflow is as follows:

1. Website code is initially created and exported from the Brizy platform
2. The exported code is imported into this template (replacing previous versions)
3. We then enhance the website with additional functionality and improvements that are beyond the capabilities of the Brizy platform due to its limitations
4. This approach allows us to leverage Brizy's visual builder while extending its functionality through custom code

### Version Control Workflow

This project uses a straightforward Git workflow to accommodate Brizy's export process:

1. Each time we receive a new export from Brizy, we completely replace the affected files
2. We then re-apply our custom modifications according to the `MODIFICATIONS.md` file
3. All custom modifications are technical fixes that remain consistent across Brizy exports
4. Version history is maintained through Git commits on the main branch

## HTML Formatting Guidelines

### Important: Do Not Modify HTML Formatting

-   **NEVER** reformat or prettify HTML files from Brizy exports
-   Brizy generates HTML with specific class structures and inline styles that are tightly coupled with its rendering engine
-   Even minor formatting changes can break the layout and functionality of Brizy-generated components
-   All HTML files are excluded in `.prettierignore` to prevent accidental formatting
-   When editing HTML, maintain the exact spacing, indentation, and line breaks of the original file
-   If you need to add custom elements, follow the existing formatting pattern precisely

### Working with Brizy HTML

-   Add custom code in dedicated sections when possible, rather than modifying existing Brizy elements
-   When modifying Brizy elements is necessary, make minimal changes and preserve all class names and attributes
-   Test thoroughly after any HTML modifications to ensure the layout renders correctly

## CSS and JavaScript Guidelines

### Prettier Configuration

-   Do NOT format HTML files with Prettier as it breaks the CSS in Brizy platform projects
-   All HTML files are excluded in `.prettierignore` for this reason
-   CSS classes and inline styles in Brizy HTML are tightly coupled with the platform's rendering engine

## Standard Modifications

All standard modifications that need to be applied after each Brizy export are documented in the `MODIFICATIONS.md` file. These modifications address technical limitations of the Brizy platform and remain consistent across different exports.

For AI assistants working on this project, specific workflow instructions are provided in the `.augment-guidelines` file.

## Technical Reference for Brizy Modifications

### File Editing Best Practices

-   **NEVER use str-replace-editor on Brizy HTML files** - The IDE auto-formatting will break the single-line structure
-   **Use command-line tools (sed) instead** for HTML modifications to preserve Brizy's formatting
-   **Test one file first** before applying bulk changes to ensure the sed commands work correctly
-   **Handle terminal timeouts gracefully** - commands may appear to hang but complete successfully

### Asset Path Patterns

-   **One level deep** (e.g., `contact/index.html`): Use `../assets/` for all asset references
-   **Two levels deep** (e.g., `en/contact/index.html`): Use `../../assets/` for all asset references
-   **Common asset reference patterns to update**:
    -   `href="assets/` → `href="../assets/` (or `../../assets/`)
    -   `src="assets/` → `src="../assets/` (or `../../assets/`)
    -   `srcset="assets/` → `srcset="../assets/` (or `../../assets/`)
    -   `url("assets/` → `url("../assets/` (or `../../assets/`)
    -   Catch-all: `assets/` → `../assets/` (or `../../assets/`)

### Canonical Tag Patterns

-   **Homepage**: `href=""` → `href="https://domain.com/"`
-   **Default language pages**: `href="/page"` → `href="https://domain.com/page/"`
-   **Additional language pages**: `href="/page-en"` → `href="https://domain.com/en/page/"`

### Bulk Operations Strategy

-   **Process files in batches** by directory depth to avoid path confusion
-   **Use loops in shell commands** for efficiency: `for dir in list; do commands; done`
-   **Always verify changes** on a sample file before applying to all files
-   **Fix inconsistent paths** that may result from multiple sed operations

### Navigation Link Issues

**The `/home` and `/en/home` Problem**: Brizy may generate navigation links pointing to `/home` or place English content at `/en/home/<USER>'t work properly in self-hosted environments. Always fix `/home` navigation links to point to `/` and move English homepage from `/en/home/<USER>/en/index.html` for cleaner URLs.

### Git Workflow for Brizy Updates

1. Backup your current project state (optional)
2. Import the new Brizy export files, replacing the existing files
3. Use the trigger instruction "Apply Brizy modifications" or manually re-apply all modifications from the `MODIFICATIONS.md` file
4. Test thoroughly to ensure all modifications work as expected
5. Git commits will be made automatically after each modification step is completed, using the step title as the commit message
