# Standard Modifications for Brizy Exports

This document contains the checklist and instructions for modifications that need to be applied after each Brizy export.

## Project Configuration

**IMPORTANT**: Update these values for your specific project before applying modifications.

```
PROJECT_DOMAIN = "www.cymta.org"  # Replace with your actual domain (no https:// or trailing slash)
DEFAULT_LANGUAGE = "el"         # Primary language code (e.g., "en", "fr", "de")
ADDITIONAL_LANGUAGES = ["en"]   # Additional language codes as an array (e.g., ["el", "fr", "es"])
```

## Modification Checklist

Use this checklist to track your progress as you apply modifications:

-   [ ] [1. Restructure page organization](#1-restructure-page-organization)
-   [ ] [2. Fix asset paths after restructuring](#2-fix-asset-paths-after-restructuring)
-   [ ] [3. Fix internal links (remove .html extensions)](#3-fix-internal-links)
-   [ ] [4. Add canonical tags](#4-add-canonical-tags)
-   [ ] [5. Add language tags (hreflang)](#5-add-language-tags-hreflang)
-   [ ] [6. Create sitemap.xml](#6-create-sitemapxml)
-   [ ] [7. Create robots.txt](#7-create-robotstxt)
-   [ ] [8. Replace form submission URLs](#8-replace-form-submission-urls)
-   [ ] [9. Fix navigation links and English homepage structure](#9-fix-navigation-links)

## Important Reminders

**ALWAYS REMEMBER:**

1. **Check off completed tasks** in this checklist as you finish each modification
2. **No trailing slashes in canonical URLs** (except homepage which should have trailing slash)
3. **Use sed commands** to preserve Brizy's HTML formatting
4. **Commit after each step** using the step title as commit message

## Modifications

### 1. Restructure Page Organization

**Goal**: Move HTML files from flat structure to organized directories

**Process**:

1. Create directories for each page (e.g., `contact/`, `about/`)
2. Move `page-name.html` → `page-name/index.html`
3. Create language subdirectories (e.g., `en/`)
4. Move `page-name-en.html` → `en/page-name/index.html`

**Example**: `contact.html` → `contact/index.html`, `contact-en.html` → `en/contact/index.html`

### 2. Fix Asset Paths After Restructuring

**Goal**: Update asset paths so CSS, JS, and images work after moving files to subdirectories

**Process**:

1. **One level deep** (e.g., `contact/index.html`): Change `assets/` → `../assets/`
2. **Two levels deep** (e.g., `el/contact/index.html`): Change `assets/` → `../../assets/`
3. **Root index.html**: Leave as `assets/` (don't change)

**Common patterns**: `href="assets/`, `src="assets/`, `url("assets/`

**Use sed commands** to preserve Brizy formatting

### 3. Fix Internal Links

**Goal**: Remove `.html` extensions from internal navigation links

**Process**:

1. Find links like `href="/contact.html"` → change to `href="/contact"`
2. Update language links: `href="/contact-el.html"` → `href="/el/contact"`
3. Don't modify external links or asset links
4. Test all navigation after changes

**Common patterns**: `href="/page-name.html"`, `href="/page-name-en.html"`

### 4. Add Canonical Tags

**Goal**: Add proper canonical tags to all pages for SEO

**Process**:

1. Add to `<head>` section: `<link rel="canonical" href="https://PROJECT_DOMAIN/page-path" />`
2. **No trailing slash** (except homepage)
3. Use current page's URL (not default language version)

**Examples**:

-   `contact/index.html` → `href="https://PROJECT_DOMAIN/contact"`
-   `en/contact/index.html` → `href="https://PROJECT_DOMAIN/en/contact"`

### 5. Add Language Tags (hreflang)

**Goal**: Add hreflang tags for multilingual SEO

**Process**:

1. Add to `<head>` section of each page
2. Include links to all language versions of that page
3. Default language gets `hreflang="x-default"` too

**Template**:

```html
<link
    rel="alternate"
    hreflang="DEFAULT_LANGUAGE"
    href="https://PROJECT_DOMAIN/page/"
/>
<link
    rel="alternate"
    hreflang="ADDITIONAL_LANGUAGE"
    href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page/"
/>
<link
    rel="alternate"
    hreflang="x-default"
    href="https://PROJECT_DOMAIN/page/"
/>
```

### 6. Create sitemap.xml

**Goal**: Create sitemap with all pages and language versions

**Process**:

1. Create `sitemap.xml` in root directory
2. Include all pages (both default and language versions)
3. Add hreflang annotations for each URL
4. Homepage priority=1.0, others priority=0.8

**Basic structure**: Each page needs separate `<url>` entries for each language, with hreflang links to all versions

### 7. Create robots.txt

**Goal**: Create robots.txt file for SEO

**Process**:

1. Create `robots.txt` in root directory
2. Content:

```
User-agent: *
Allow: /

Sitemap: https://PROJECT_DOMAIN/sitemap.xml
```

### 8. Replace Form Submission URLs

**Goal**: Update form action URLs to custom endpoints

**Process**:

1. **Ask user first** - never replace without confirmation
2. Search for `action="https://msg.formsender.online` or similar
3. Replace with: `https://api.melo.systems/api:VQXw_Ykg/email-form`
4. Only change the `action` URL, leave everything else unchanged

### 9. Fix Navigation Links

**Goal**: Fix `/home` navigation links and English homepage structure

**Common Issues**:

-   Navigation links pointing to `href="/home"` (should be `href="/"`)
-   English homepage at `/en/home/<USER>/en/`)

**Process**:

1. Replace all `href="/home"` with `href="/"`
2. Move `/en/home/<USER>/en/index.html` if needed
3. Update asset paths from `../../assets/` to `../assets/` after moving
4. Update canonical URLs, hreflang tags, and sitemap entries
5. Use sed commands to preserve Brizy formatting

## How to Apply These Modifications

1. **Work sequentially** - complete each step before moving to the next
2. **Test after each step** - verify changes work correctly
3. **Commit after each step** - use step title as commit message
4. **Use sed commands** - preserve Brizy's HTML formatting
5. **Check off completed tasks** in the checklist above
